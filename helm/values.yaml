# TencentBlueKing is pleased to support the open source community by making
# 蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
# Copyright (C) 2024 THL A29 Limited,
# a Tencent company. All rights reserved.
# Licensed under the MIT License (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at http://opensource.org/licenses/MIT
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on
# an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied. See the License for the
# specific language governing permissions and limitations under the License.

# @section 通用配置
image:
  # @param 镜像服务地址
  registry: hub.bktencent.com
  # @param 仓库路径
  repository: blueking/bkflow
  # @param 标签
  tag: 1.11.1
  # @param 镜像拉取策略
  pullPolicy: IfNotPresent

# @section 资源配置
resources:
  limits:
    # @param cpu limit
    cpu: 1
    # @param memory limit
    memory: 1Gi
  requests:
    # @param cpu limit
    cpu: 1
    # @param memory limit
    memory: 1Gi

# @section 通用环境变量配置
common:
  env:
    # @param 运行版本
    RUN_VER: "open"
    # @param 运行模式
    RUN_MODE: "PRODUCT"
    # @param 时区
    TZ: "Asia/Shanghai"
    # @param 日志目录
    LOG_DIR: "/app/logs"
    # @param 蓝鲸PaaS平台地址
    BK_PAAS_HOST: "paas.example.com"
    # @param 蓝鲸PaaS平台端口
    BK_PAAS_HTTP_PORT: 80
    # @param 蓝鲸应用ID
    APP_CODE: "bk_flow_engine"
    # @param 蓝鲸应用Token
    APP_TOKEN: "xxxxx_token"
    # @param 蓝鲸API网关地址
    BK_APIGW_URL_TMPL: "http://bkapi.example.com/api/{api_name}/"
    # @param 蓝鲸API网关名称
    BK_APIGW_NAME: "bk_flow_engine"
    # @param 内部模块调用Token
    APP_INTERNAL_TOKEN: "df1da1fc5b134858b0743838d7db256b"
    # @param 默认引擎模块内部调用Token
    DEFAULT_ENGINE_APP_INTERNAL_TOKEN: "a35230d7a282485dba66a867e07d8125"
    # @param 默认回调KEY
    BKFLOW_DEFAULT_CALLBACK_KEY: "jbSH1_3PFsM8WRZZpUXJPhlJuvuA44A7Ov0nPhFk5ZY="
    # @param 是否使用插件服务
    BKAPP_USE_PLUGIN_SERVICE: "1"
    # @param 数据库地址
    MYSQL_HOST: "bkflow-mysql"
    # @param 数据库端口
    MYSQL_PORT: 3306
    # @param 数据库用户
    MYSQL_USER: "root"
    # @param 数据库密码
    MYSQL_PASSWORD: "example"
    # @param 数据库名
    MYSQL_NAME: "bkflow"
    # @param Redis地址
    REDIS_HOST: "bkflow-redis"
    # @param Redis端口
    REDIS_PORT: 6379
    # @param Redis密码
    REDIS_PASSWORD: "example"
    # @param RabbitMQ地址
    RABBITMQ_HOST: "bkflow-rabbitmq"
    # @param RabbitMQ端口
    RABBITMQ_PORT: 5672
    # @param RabbitMQ用户
    RABBITMQ_USER: "guest"
    # @param RabbitMQ密码
    RABBITMQ_PASSWORD: "guest"
    # @param RabbitMQ vhost
    RABBITMQ_VHOST: "/"

# @section Interface模块配置
interface:
  # @param 是否启用Interface模块
  enabled: true
  # @param Interface模块特定环境变量
  env:
    # @param 模块类型
    BKFLOW_MODULE_TYPE: "interface"
    # @param Gunicorn Worker数量
    GUNICORN_WORKER_NUM: "1"
    # @param Gunicorn Thread数量
    GUNICORN_THREAD_NUM: "10"

  # @section Interface Web服务配置
  web:
    # @param 实例数
    replicas: 2
    # @param HTTP端口
    port: 8000
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 1
        # @param 内存限制
        memory: 1Gi
      requests:
        # @param CPU请求
        cpu: 500m
        # @param 内存请求
        memory: 512Mi

  # @section Interface Celery Beat配置
  beat:
    # @param 是否启用
    enabled: true
    # @param 实例数
    replicas: 1
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 500m
        # @param 内存限制
        memory: 512Mi
      requests:
        # @param CPU请求
        cpu: 200m
        # @param 内存请求
        memory: 256Mi

  # @section Interface Celery Worker配置
  worker:
    # @param 是否启用
    enabled: true
    # @param 实例数
    replicas: 2
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 1
        # @param 内存限制
        memory: 1Gi
      requests:
        # @param CPU请求
        cpu: 500m
        # @param 内存请求
        memory: 512Mi

# @section Engine模块配置
engine:
  # @param 是否启用Engine模块
  enabled: true
  # @param Engine模块特定环境变量
  env:
    # @param 模块类型
    BKFLOW_MODULE_TYPE: "engine"
    # @param 模块编码
    BKFLOW_MODULE_CODE: "default"
    # @param Gunicorn Worker数量
    GUNICORN_WORKER_NUM: "2"
    # @param Gunicorn Thread数量
    GUNICORN_THREAD_NUM: "10"
    # @param Interface模块内部调用Token
    INTERFACE_APP_INTERNAL_TOKEN: "df1da1fc5b134858b0743838d7db256b"

  # @section Engine Web服务配置
  web:
    # @param 实例数
    replicas: 2
    # @param HTTP端口
    port: 8000
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 1
        # @param 内存限制
        memory: 1Gi
      requests:
        # @param CPU请求
        cpu: 500m
        # @param 内存请求
        memory: 512Mi

  # @section Engine Beat配置
  beat:
    # @param 是否启用
    enabled: true
    # @param 实例数
    replicas: 1
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 500m
        # @param 内存限制
        memory: 512Mi
      requests:
        # @param CPU请求
        cpu: 200m
        # @param 内存请求
        memory: 256Mi

  # @section Engine ER Execute Worker配置
  erExecute:
    # @param 是否启用
    enabled: true
    # @param 实例数
    replicas: 2
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 2
        # @param 内存限制
        memory: 2Gi
      requests:
        # @param CPU请求
        cpu: 1
        # @param 内存请求
        memory: 1Gi

  # @section Engine ER Schedule Worker配置
  erSchedule:
    # @param 是否启用
    enabled: true
    # @param 实例数
    replicas: 2
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 2
        # @param 内存限制
        memory: 2Gi
      requests:
        # @param CPU请求
        cpu: 1
        # @param 内存请求
        memory: 1Gi

  # @section Engine Common Worker配置
  commonWorker:
    # @param 是否启用
    enabled: true
    # @param 实例数
    replicas: 2
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 1
        # @param 内存限制
        memory: 1Gi
      requests:
        # @param CPU请求
        cpu: 500m
        # @param 内存请求
        memory: 512Mi

  # @section Engine Timeout Worker配置
  timeoutWorker:
    # @param 是否启用
    enabled: true
    # @param 实例数
    replicas: 1
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 500m
        # @param 内存限制
        memory: 512Mi
      requests:
        # @param CPU请求
        cpu: 200m
        # @param 内存请求
        memory: 256Mi

  # @section Engine Clean Worker配置
  cleanWorker:
    # @param 是否启用
    enabled: true
    # @param 实例数
    replicas: 1
    # @param 资源配置
    resources:
      limits:
        # @param CPU限制
        cpu: 500m
        # @param 内存限制
        memory: 512Mi
      requests:
        # @param CPU请求
        cpu: 200m
        # @param 内存请求
        memory: 256Mi

# @section 初始化任务配置
initJob:
  # @param 是否启用初始化任务
  enabled: true
  # @param 资源配置
  resources:
    limits:
      # @param CPU限制
      cpu: 500m
      # @param 内存限制
      memory: 512Mi
    requests:
      # @param CPU请求
      cpu: 200m
      # @param 内存请求
      memory: 256Mi

# @section 服务配置
service:
  # @param 服务类型
  type: ClusterIP
  # @param Interface模块服务端口
  interfacePort: 8000
  # @param Engine模块服务端口
  enginePort: 8001

# @section Ingress配置
ingress:
  # @param 是否启用Ingress
  enabled: true
  # @param Ingress类名
  className: "nginx"
  # @param 注解
  annotations: {}
  # @param 主机配置
  hosts:
    - host: bkflow.example.com
      paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: bkflow-interface
              port:
                number: 8000
  # @param TLS配置
  tls: []

# @section 健康检查配置
healthCheck:
  # @section 活跃探测器配置
  livenessProbe:
    # @param 是否启用活跃探测器
    enabled: true
    # @param 初次探测前等待时长
    initialDelaySeconds: 30
    # @param 探测间隔
    periodSeconds: 30
    # @param 超时等待时长
    timeoutSeconds: 10
    # @param 最小连续成功数
    successThreshold: 1
    # @param 探测失败重试次数
    failureThreshold: 3
    # @param HTTP路径
    httpGet:
      path: /healthz/
      port: 8000

  # @section 就绪探测器配置
  readinessProbe:
    # @param 是否启用就绪探测器
    enabled: true
    # @param 初次探测前等待时长
    initialDelaySeconds: 10
    # @param 探测间隔
    periodSeconds: 10
    # @param 超时等待时长
    timeoutSeconds: 5
    # @param 最小连续成功数
    successThreshold: 1
    # @param 探测失败重试次数
    failureThreshold: 3
    # @param HTTP路径
    httpGet:
      path: /healthz/
      port: 8000

# @section 节点亲和性配置
affinity: {}

# @section 节点选择器配置
nodeSelector: {}

# @section 容忍度配置
tolerations: []

# @section Pod安全上下文配置
podSecurityContext: {}

# @section 安全上下文配置
securityContext: {}

# @section 服务账户配置
serviceAccount:
  # @param 是否创建服务账户
  create: true
  # @param 服务账户注解
  annotations: {}
  # @param 服务账户名称
  name: ""

# @section Pod注解配置
podAnnotations: {}

# @section Pod标签配置
podLabels: {}
