{{/* vim: set filetype=mustache: */}}

{{- define "bkbase-common.api.importms.fullname" -}}
  {{- $fullname := include "common.names.fullname" . -}}
  {{- printf "%s-importms" $fullname -}}
{{- end -}}

{{- define "bkbase-common.api.importms.labels.standard" -}}
app.kubernetes.io/name: {{ include "bkbase-common.api.importms.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Generate API-Jobs importms content

Usage:
{{ include "bkbase-common.api.importms" . }}
*/}}
{{- define "bkbase-common.api.importms" -}}
apiVersion: batch/v1
kind: Job
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "bkbase-common.api.importms.fullname" . }}
  labels:
    {{- include "bkbase-common.api.importms.labels.standard" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install
    "helm.sh/hook-weight": "-5"
    "helm.sh/hook-delete-policy": "before-hook-creation"
spec:
  backoffLimit: 0
  template:
    metadata:
      labels:
        {{- include "bkbase-common.api.importms.labels.standard" . | nindent 8 }}
    spec:
      initContainers:
        - name: wait-metaapi
          image: busybox:1.28
          command: ['sh', '-c', 'until nslookup {{ .Values.api.env.BKDATA_METAAPI_HOST }}; do echo waiting for metaapi; sleep 2; done']
          resources:
            limits:
              cpu: 100m
              memory: 64Mi
            requests:
              cpu: 100m
              memory: 64Mi
      containers:
        - name: {{ include "bkbase-common.api.importms.fullname" . }}
          image: {{ include "common.images.image" ( dict "imageRoot" .Values.image ) }}
          imagePullPolicy: {{ .Values.image.pullPolicy | default "IfNotPresent" }}
          command: ["bash"]
          args: ["/app/code/import_ms.sh"]
          resources:
            {{- toYaml .Values.importms.resources | nindent 12 }}
          env:
            {{- include "bkbase-common.api.envs" . | nindent 12 }}
      restartPolicy: Never
{{- end -}}


{{- define "bkbase-common.api.postinstall.fullname" -}}
  {{- $fullname := include "common.names.fullname" . -}}
  {{- printf "%s-postinstall" $fullname -}}
{{- end -}}

{{- define "bkbase-common.api.postinstall.labels.standard" -}}
app.kubernetes.io/name: {{ include "bkbase-common.api.postinstall.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}


{{/*
Generate API-Jobs postinstall content

Usage:
{{ include "bkbase-common.api.postinstall" . }}
*/}}
{{- define "bkbase-common.api.postinstall" -}}
apiVersion: batch/v1
kind: Job
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "bkbase-common.api.postinstall.fullname" . }}
  labels:
    {{- include "bkbase-common.api.postinstall.labels.standard" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install
    "helm.sh/hook-weight": "-5"
    "helm.sh/hook-delete-policy": "before-hook-creation"
spec:
  backoffLimit: 0
  template:
    metadata:
      labels:
        {{- include "bkbase-common.api.postinstall.labels.standard" . | nindent 8 }}
    spec:
      containers:
        - name: {{ include "bkbase-common.api.postinstall.fullname" . }}
          image: {{ include "common.images.image" ( dict "imageRoot" .Values.image ) }}
          imagePullPolicy: {{ .Values.image.pullPolicy | default "IfNotPresent" }}
          command: ["bash"]
          args: ["/app/code/postinstall.sh"]
          resources:
            {{- toYaml .Values.postinstall.resources | nindent 12 }}
          env:
            {{- include "bkbase-common.api.envs" . | nindent 12 }}
      restartPolicy: Never
{{- end -}}

