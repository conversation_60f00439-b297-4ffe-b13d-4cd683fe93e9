{{/* vim: set filetype=mustache: */}}

{{- define "bkbase-common.api.celerybeat.fullname" -}}
  {{- $fullname := include "common.names.fullname" . -}}
  {{- printf "%s-celerybeat" $fullname -}}
{{- end -}}

{{- define "bkbase-common.api.celerybeat.labels.standard" -}}
app.kubernetes.io/name: {{ include "bkbase-common.api.celerybeat.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end -}}

{{/*
Labels to use on deploy.spec.selector.matchLabels and svc.spec.selector
*/}}
{{- define "bkbase-common.api.celerybeat.labels.matchLabels" -}}
app.kubernetes.io/name: {{ include "bkbase-common.api.celerybeat.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}


{{- define "bkbase-common.api.envs" -}}
{{- range $k, $v := .Values.pizza.env }}
- name: {{ $k }}
  value: "{{ $v }}"
{{- end }}
{{- range $k, $v := .Values.api.env }}
- name: {{ $k }}
  value: "{{ $v }}"
{{- end }}
{{- end -}}


{{/*
Generate API-Celerybeat deployment file content

Usage:
{{ include "bkbase-common.api.celerybeat" . }}
*/}}
{{- define "bkbase-common.api.celerybeat.deployment" -}}
apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: Deployment
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "bkbase-common.api.celerybeat.fullname" . }}
  labels:
    {{- include "bkbase-common.api.celerybeat.labels.standard" . | nindent 4 }}
spec:
  replicas: {{ .Values.celerybeat.replicas }}
  selector:
    matchLabels:
      {{- include "bkbase-common.api.celerybeat.labels.matchLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "bkbase-common.api.celerybeat.labels.matchLabels" . | nindent 8 }}
    spec:
      containers:
        - name: {{ include "bkbase-common.api.celerybeat.fullname" . }}
          image: {{ include "common.images.image" ( dict "imageRoot" .Values.image ) }}
          imagePullPolicy: {{ .Values.image.pullPolicy | default "IfNotPresent" }}
          command: ["/bin/bash", "./start_celery_beat.sh"]
          resources:
            {{- toYaml .Values.celerybeat.resources | nindent 12 }}
          env:
            {{- include "bkbase-common.api.envs" . | nindent 12 }}
{{- end -}}

{{- define "bkbase-common.api.celeryworker.fullname" -}}
    {{- $fullname := include "common.names.fullname" . -}}
    {{- printf "%s-celeryworker" $fullname -}}
{{- end -}}

{{- define "bkbase-common.api.celeryworker.labels.standard" -}}
app.kubernetes.io/name: {{ include "bkbase-common.api.celeryworker.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end -}}

{{/*
Labels to use on deploy.spec.selector.matchLabels and svc.spec.selector
*/}}
{{- define "bkbase-common.api.celeryworker.labels.matchLabels" -}}
app.kubernetes.io/name: {{ include "bkbase-common.api.celeryworker.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Generate API-Celeryworker deployment file content

Usage:
{{ include "bkbase-common.api.celeryworker" . }}
*/}}
{{- define "bkbase-common.api.celeryworker.deployment" -}}
apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: Deployment
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "bkbase-common.api.celeryworker.fullname" . }}
  labels:
    {{- include "bkbase-common.api.celeryworker.labels.standard" . | nindent 4 }}
spec:
  replicas: {{ .Values.celeryworker.replicas }}
  selector:
    matchLabels:
      {{- include "bkbase-common.api.celeryworker.labels.matchLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "bkbase-common.api.celeryworker.labels.matchLabels" . | nindent 8 }}
    spec:
      {{- if .Values.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.affinity "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.celeryworker.terminationGrace }}
      terminationGracePeriodSeconds: {{ .Values.celeryworker.terminationGracePeriodSeconds | default 600 }}
      {{- end }}
      containers:
        - name: {{ include "bkbase-common.api.celeryworker.fullname" . }}
          image: {{ include "common.images.image" ( dict "imageRoot" .Values.image ) }}
          imagePullPolicy: {{ .Values.image.pullPolicy | default "IfNotPresent" }}
          command: ["/bin/bash", "./start_celery.sh"]
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            {{- include "bkbase-common.api.envs" . | nindent 12 }}
          {{- if .Values.celeryWorkerLivenessProbe }}
          {{- if .Values.celeryWorkerLivenessProbe.enabled }}
          livenessProbe:
            exec:
              command: {{ toYaml .Values.celeryWorkerLivenessProbe.exec.command | nindent 16 }}
            initialDelaySeconds: {{ .Values.celeryWorkerLivenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.celeryWorkerLivenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.celeryWorkerLivenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.celeryWorkerLivenessProbe.successThreshold }}
            failureThreshold: {{ .Values.celeryWorkerLivenessProbe.failureThreshold }}
          {{- end }}
          {{- end }}
          {{- if .Values.celeryWorkerReadinessProbe }}
          {{- if .Values.celeryWorkerReadinessProbe.enabled }}
          readinessProbe:
            exec:
              command: {{ toYaml .Values.celeryWorkerReadinessProbe.exec.command | nindent 16 }}
            initialDelaySeconds: {{ .Values.celeryWorkerReadinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.celeryWorkerReadinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.celeryWorkerReadinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.celeryWorkerReadinessProbe.successThreshold }}
            failureThreshold: {{ .Values.celeryWorkerReadinessProbe.failureThreshold }}
          {{- end }}
          {{- end }}
          {{- if .Values.celeryworker.terminationGrace }}
          lifecycle:
            preStop:
              exec:
                command: ["/bin/bash","/app/code/stop_celery_gracefully.sh"]
          {{- end }}
          volumeMounts:
          {{- if .Values.maven }}
          {{- if .Values.maven.enabled }}
            - name: maven-config-v
              mountPath: /opt/apache-maven-3.8.6/conf/settings.xml
              subPath: settings.xml
          {{- end }}
          {{- end }}
          {{- if .Values.stormConfig }}
          {{- if .Values.stormConfig.enabled }}
            - name: storm-config-v
              mountPath: /app/code/dataflow/stream/extend/support/conf/stream.config
              subPath: stream.config
          {{- end }}
          {{- end }}
      volumes:
      {{- if .Values.maven }}
      {{- if .Values.maven.enabled }}
        - name: maven-config-v
          configMap:
            name: maven-config
            items:
              - key: settings.xml
                path: settings.xml
      {{- end }}
      {{- end }}
      {{- if .Values.stormConfig }}
      {{- if .Values.stormConfig.enabled }}
        - name: storm-config-v
          configMap:
            name: storm-config
            items:
              - key: stream.config
                path: stream.config
      {{- end }}
      {{- end }}
      {{- if .Values.dnsConfig }}
      {{- if .Values.dnsConfig.enabled }}
      dnsConfig:
        searches: {{ toYaml .Values.dnsConfig.searches | nindent 10 }}
      {{- end }}
      {{- end }}
{{- end -}}


{{/*
Generate API deployment file content

Usage:
{{ include "bkbase-common.api.deployment" . }}
*/}}
{{- define "bkbase-common.api.deployment" -}}
apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: Deployment
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "common.names.fullname" . }}
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
spec:
  replicas: {{ .Values.api.replicas }}
  selector:
    matchLabels:
      {{- include "common.labels.matchLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "common.labels.matchLabels" . | nindent 8 }}
    spec:
      {{- if .Values.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.affinity "context" $) | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ include "common.names.fullname" . }}
          image: {{ include "common.images.image" ( dict "imageRoot" .Values.image ) }}
          imagePullPolicy: {{ .Values.image.pullPolicy | default "IfNotPresent" }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.urlpath }}
              port: {{ .Values.livenessProbe.port }}
              httpHeaders:
              - name: User-Agent
                value: k8s-pod-check
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.urlpath }}
              port: {{ .Values.readinessProbe.port }}
              httpHeaders:
              - name: User-Agent
                value: k8s-pod-check
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            {{- include "bkbase-common.api.envs" . | nindent 12 }}
          volumeMounts:
          {{- if .Values.stormConfig }}
          {{- if .Values.stormConfig.enabled }}
            - name: storm-config-v
              mountPath: /app/code/dataflow/stream/extend/support/conf/stream.config
              subPath: stream.config
          {{- end }}
          {{- end }}
          {{- if.Values.kubectlConfig }}
          {{- if .Values.kubectlConfig.enabled }}
            - name: kubectl-config-v
              mountPath: /root/.kube/
          {{- end }}
          {{- end }}
          {{- if .Values.maven }}
          {{- if .Values.maven.enabled }}
            - name: maven-config-v
              mountPath: /opt/apache-maven-3.8.6/conf/settings.xml
              subPath: settings.xml
          {{- end }}
          {{- end }}
          {{- if .Values.persistence }}
          {{- if .Values.persistence.enabled }}
            - name: {{ include "common.names.fullname" . }}-volumes
              mountPath: {{ .Values.persistence.mountPath }}
          {{- end }}
          {{- end }}
      volumes:
      {{- if .Values.stormConfig }}
      {{- if .Values.stormConfig.enabled }}
        - name: storm-config-v
          configMap:
            name: storm-config
            items:
              - key: stream.config
                path: stream.config
      {{- end }}
      {{- end }}
      {{- if .Values.kubectlConfig }}
      {{- if .Values.kubectlConfig.enabled }}
        - name: kubectl-config-v
          configMap:
            name: kubectl-config
            items:
              - key: kubectl.config
                path: config
      {{- end }}
      {{- end }}
      {{- if .Values.persistence }}
      {{- if .Values.persistence.enabled }}
        - name: {{ include "common.names.fullname" . }}-volumes
          persistentVolumeClaim:
            claimName: {{ include "common.names.fullname" . }}-pvc
      {{- end }}
      {{- end }}
      {{- if .Values.maven }}
      {{- if .Values.maven.enabled }}
        - name: maven-config-v
          configMap:
            name: maven-config
            items:
              - key: settings.xml
                path: settings.xml
      {{- end }}
      {{- end }}
      {{- if .Values.dnsConfig }}
      {{- if .Values.dnsConfig.enabled }}
      dnsConfig:
        searches: {{ toYaml .Values.dnsConfig.searches | nindent 10 }}
      {{- end }}
      {{- end }}
{{- end -}}
