{{/* vim: set filetype=mustache: */}}

{{/*
Generate API-Ingress file content

Usage:
{{ include "bkbase-common.api.ingress" . }}
*/}}
{{- define "bkbase-common.api.ingress" -}}
{{- if (default true .Values.globalIngress.disabled) }}
{{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1
{{- else if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1beta1
{{- else -}}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ include "common.names.fullname" . }}
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
spec:
  {{- if semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion }}
  ingressClassName: nginx
  {{- end }}
  rules:
    - host: {{ .host | quote }}
      http:
        paths:
          - path: /v3/{{ .Values.api.env.APP_NAME }}/
            {{- if semverCompare ">=1.18-0" $.Capabilities.KubeVersion.GitVersion }}
            pathType: Prefix
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ include "common.names.fullname" . }}
                port:
                  number: {{ .Values.api.http.port }}
              {{- else }}
              serviceName: {{ include "common.names.fullname" . }}
              servicePort: {{ .Values.api.http.port }}
              {{- end }}
{{- end }}
{{- end -}}


{{/*
Generate API-Service file content

Usage:
{{ include "bkbase-common.api.service" . }}
*/}}
{{- define "bkbase-common.api.service" -}}
apiVersion: v1
kind: Service
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "common.names.fullname" . }}
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
spec:
  {{- if .Values.ipFamily }}
  {{- toYaml .Values.ipFamily | nindent 2 }}
  {{- end }}
  selector:
    {{- include "common.labels.matchLabels" . | nindent 4 }}
  ports:
    - port: {{ .Values.api.http.port }}
      targetPort: {{ .Values.api.http.port }}
      protocol: TCP
      name: http
{{- end -}}
