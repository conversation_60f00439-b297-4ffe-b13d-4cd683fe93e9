{{/* vim: set filetype=mustache: */}}

{{/*
Engine Module Deployment
*/}}
{{- define "bkflow-common.engine.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-engine
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: engine
spec:
  replicas: {{ .Values.modules.engine.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: engine
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: engine
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: engine
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["./container_start.sh"]
          ports:
            - name: http
              containerPort: {{ .Values.modules.engine.web.port }}
              protocol: TCP
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.engine.moduleType | quote }}
            - name: BKFLOW_MODULE_CODE
              value: {{ .Values.modules.engine.moduleCode | quote }}
            - name: PORT
              value: {{ .Values.modules.engine.web.port | quote }}
            - name: GUNICORN_WORKER_NUM
              value: {{ .Values.modules.engine.web.gunicorn.workerNum | quote }}
            - name: GUNICORN_THREAD_NUM
              value: {{ .Values.modules.engine.web.gunicorn.threadNum | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe.httpGet | nindent 12 }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe.httpGet | nindent 12 }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          resources:
            {{- toYaml .Values.modules.engine.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{/*
Engine ER Execute Worker Deployment
*/}}
{{- define "bkflow-common.engine.er_e.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-engine-er-e
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: engine-er-e
spec:
  replicas: {{ .Values.modules.engine.processes.er_e.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: engine-er-e
  template:
    metadata:
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: engine-er-e
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: er-e
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - {{ .Values.modules.engine.processes.er_e.command | quote }}
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.engine.moduleType | quote }}
            - name: BKFLOW_MODULE_CODE
              value: {{ .Values.modules.engine.moduleCode | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          resources:
            {{- toYaml .Values.modules.engine.processes.er_e.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{/*
Engine ER Schedule Worker Deployment
*/}}
{{- define "bkflow-common.engine.er_s.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-engine-er-s
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: engine-er-s
spec:
  replicas: {{ .Values.modules.engine.processes.er_s.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: engine-er-s
  template:
    metadata:
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: engine-er-s
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: er-s
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - {{ .Values.modules.engine.processes.er_s.command | quote }}
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.engine.moduleType | quote }}
            - name: BKFLOW_MODULE_CODE
              value: {{ .Values.modules.engine.moduleCode | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          resources:
            {{- toYaml .Values.modules.engine.processes.er_s.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{/*
Engine Common Worker Deployment
*/}}
{{- define "bkflow-common.engine.cworker.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-engine-cworker
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: engine-cworker
spec:
  replicas: {{ .Values.modules.engine.processes.cworker.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: engine-cworker
  template:
    metadata:
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: engine-cworker
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: cworker
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - {{ .Values.modules.engine.processes.cworker.command | quote }}
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.engine.moduleType | quote }}
            - name: BKFLOW_MODULE_CODE
              value: {{ .Values.modules.engine.moduleCode | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          resources:
            {{- toYaml .Values.modules.engine.processes.cworker.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{/*
Engine Timeout Worker Deployment
*/}}
{{- define "bkflow-common.engine.timeout.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-engine-timeout
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: engine-timeout
spec:
  replicas: {{ .Values.modules.engine.processes.timeout.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: engine-timeout
  template:
    metadata:
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: engine-timeout
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: timeout
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - {{ .Values.modules.engine.processes.timeout.command | quote }}
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.engine.moduleType | quote }}
            - name: BKFLOW_MODULE_CODE
              value: {{ .Values.modules.engine.moduleCode | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          resources:
            {{- toYaml .Values.modules.engine.processes.timeout.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{/*
Engine Beat Deployment
*/}}
{{- define "bkflow-common.engine.beat.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-engine-beat
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: engine-beat
spec:
  replicas: {{ .Values.modules.engine.processes.beat.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: engine-beat
  template:
    metadata:
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: engine-beat
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: beat
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - {{ .Values.modules.engine.processes.beat.command | quote }}
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.engine.moduleType | quote }}
            - name: BKFLOW_MODULE_CODE
              value: {{ .Values.modules.engine.moduleCode | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          resources:
            {{- toYaml .Values.modules.engine.processes.beat.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{/*
Engine Clean Worker Deployment
*/}}
{{- define "bkflow-common.engine.clean_worker.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-engine-clean-worker
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: engine-clean-worker
spec:
  replicas: {{ .Values.modules.engine.processes.clean_worker.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: engine-clean-worker
  template:
    metadata:
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: engine-clean-worker
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: clean-worker
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - {{ .Values.modules.engine.processes.clean_worker.command | quote }}
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.engine.moduleType | quote }}
            - name: BKFLOW_MODULE_CODE
              value: {{ .Values.modules.engine.moduleCode | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          resources:
            {{- toYaml .Values.modules.engine.processes.clean_worker.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
