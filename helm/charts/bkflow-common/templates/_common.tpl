{{/* vim: set filetype=mustache: */}}

{{/*
Secret Template
*/}}
{{- define "bkflow-common.secret" -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "bkflow-common.fullname" . }}-secret
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
type: Opaque
data:
  app-internal-token: {{ .Values.app.appInternalToken | b64enc | quote }}
  default-engine-internal-token: {{ .Values.app.defaultEngineInternalToken | b64enc | quote }}
  default-callback-key: {{ .Values.app.defaultCallbackKey | b64enc | quote }}
{{- end }}

{{/*
ConfigMap Template
*/}}
{{- define "bkflow-common.configmap" -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "bkflow-common.fullname" . }}-config
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
data:
  # BKFlow 应用配置
  BK_APP_CODE: {{ .Values.app.bkAppCode | quote }}
  BK_APP_NAME: {{ .Values.app.bkAppName | quote }}
  BK_APIGW_NAME: {{ .Values.app.apigwName | quote }}
  BK_APIGW_NETLOC_PATTERN: {{ .Values.app.apigwNetlocPattern | quote }}
  BKAPP_USE_PLUGIN_SERVICE: {{ .Values.app.usePluginService | quote }}
  
  # 通用环境变量
  {{- range $key, $value := .Values.env }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}

{{/*
ServiceAccount Template
*/}}
{{- define "bkflow-common.serviceaccount" -}}
{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "bkflow-common.fullname" . }}
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: false
{{- end }}
{{- end }}
