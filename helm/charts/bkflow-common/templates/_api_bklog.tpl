{{/* vim: set filetype=mustache: */}}

{{- define "bkbase-common.api.bklogconfig.stdout.fullname" -}}
  {{- $fullname := include "common.names.fullname" . -}}
  {{- printf "%s-bklogconfig-stdout" $fullname -}}
{{- end -}}

{{/*
Generate API-BKlogconfig for current instance stdout log

Usage:
{{ include "bkbase-common.api.bklogconfig.stdout" . }}
*/}}
{{- define "bkbase-common.api.bklogconfig.stdout" -}}
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "bkbase-common.api.bklogconfig.stdout.fullname" . }}
spec:
  dataId: {{ .Values.bkLogConfig.dataId }}
  logConfigType: std_log_config
  namespace: {{ .Release.Namespace | quote }}
  encoding: 'utf-8'
  labelSelector:
    matchLabels:
      {{- include "common.labels.matchLabels" . | nindent 6 }}
{{- end -}}

{{- define "bkbase-common.api.bklogconfig.apilog.fullname" -}}
  {{- $fullname := include "common.names.fullname" . -}}
  {{- printf "%s-bklogconfig-apilog" $fullname -}}
{{- end -}}


{{/*
Generate API-BKlogconfig for current instance api-log

Usage:
{{ include "bkbase-common.api.bklogconfig.apilog" . }}
*/}}
{{- define "bkbase-common.api.bklogconfig.apilog" -}}
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "bkbase-common.api.bklogconfig.apilog.fullname" . }}
spec:
  dataId: {{ .Values.bkLogConfig.dataIdForApi }}
  logConfigType: container_log_config
  namespace: {{ .Release.Namespace | quote }}
  encoding: 'utf-8'
  labelSelector:
    matchLabels:
      {{- include "common.labels.matchLabels" . | nindent 6 }}
  path:
    - /app/logs/api.log
{{- end -}}


{{- define "bkbase-common.api.bklogconfig.syslog.fullname" -}}
  {{- $fullname := include "common.names.fullname" . -}}
  {{- printf "%s-bklogconfig-syslog" $fullname -}}
{{- end -}}


{{/*
Generate API-BKlogconfig for current instance api-log

Usage:
{{ include "bkbase-common.api.bklogconfig.syslog" . }}
*/}}
{{- define "bkbase-common.api.bklogconfig.syslog" -}}
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "bkbase-common.api.bklogconfig.syslog.fullname" . }}
spec:
  dataId: {{ .Values.bkLogConfig.dataIdForSys }}
  logConfigType: container_log_config
  namespace: {{ .Release.Namespace | quote }}
  encoding: 'utf-8'
  labelSelector:
    matchLabels:
      {{- include "common.labels.matchLabels" . | nindent 6 }}
  path:
    - /app/logs/sys.log
{{- end -}}

