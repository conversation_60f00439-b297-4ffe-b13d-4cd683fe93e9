{{/* vim: set filetype=mustache: */}}

{{/*
Interface Service
*/}}
{{- define "bkflow-common.interface.service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "bkflow-common.fullname" . }}-interface
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: interface
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "bkflow-common.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: interface
{{- end }}

{{/*
Engine Service
*/}}
{{- define "bkflow-common.engine.service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "bkflow-common.fullname" . }}-engine
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: engine
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "bkflow-common.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: engine
{{- end }}

{{/*
Interface Ingress
*/}}
{{- define "bkflow-common.interface.ingress" -}}
{{- if .Values.ingress.enabled -}}
{{- $fullName := include "bkflow-common.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- if and .Values.ingress.className (not (hasKey .Values.ingress.annotations "kubernetes.io/ingress.class")) }}
  {{- $_ := set .Values.ingress.annotations "kubernetes.io/ingress.class" .Values.ingress.className}}
{{- end }}
{{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1
{{- else if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1beta1
{{- else -}}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}-interface
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: interface
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.ingress.className }}
  {{- end }}
  {{- if .Values.ingress.tls }}
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if and .pathType (semverCompare ">=1.18-0" $.Capabilities.KubeVersion.GitVersion) }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ $fullName }}-interface
                port:
                  number: {{ $svcPort }}
              {{- else }}
              serviceName: {{ $fullName }}-interface
              servicePort: {{ $svcPort }}
              {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
{{- end }}
