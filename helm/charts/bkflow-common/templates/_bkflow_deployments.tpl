{{/* vim: set filetype=mustache: */}}

{{/*
BKFlow Common Helper Functions
*/}}

{{- define "bkflow-common.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{- define "bkflow-common.labels" -}}
helm.sh/chart: {{ printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
app.kubernetes.io/name: {{ include "bkflow-common.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{- define "bkflow-common.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bkflow-common.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{- define "bkflow-common.image" -}}
{{- $registry := .Values.global.imageRegistry | default .Values.image.registry -}}
{{- $repository := .Values.image.repository -}}
{{- $tag := .Values.image.tag | default .Chart.AppVersion -}}
{{- if $registry }}
{{- printf "%s/%s:%s" $registry $repository $tag }}
{{- else }}
{{- printf "%s:%s" $repository $tag }}
{{- end }}
{{- end }}

{{- define "bkflow-common.commonEnv" -}}
{{- range $k, $v := .Values.env }}
- name: {{ $k }}
  value: {{ $v | quote }}
{{- end }}
{{- if .Values.mysql.enabled }}
- name: MYSQL_HOST
  value: {{ include "bkflow-common.fullname" . }}-mysql
- name: MYSQL_PORT
  value: "3306"
- name: MYSQL_USER
  value: {{ .Values.mysql.auth.username | quote }}
- name: MYSQL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow-common.fullname" . }}-mysql
      key: mysql-password
- name: MYSQL_DATABASE
  value: {{ .Values.mysql.auth.database | quote }}
{{- end }}
{{- if .Values.rabbitmq.enabled }}
- name: RABBITMQ_HOST
  value: {{ include "bkflow-common.fullname" . }}-rabbitmq
- name: RABBITMQ_PORT
  value: "5672"
- name: RABBITMQ_USER
  value: {{ .Values.rabbitmq.auth.username | quote }}
- name: RABBITMQ_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow-common.fullname" . }}-rabbitmq
      key: rabbitmq-password
{{- end }}
{{- if .Values.redis.enabled }}
- name: REDIS_HOST
  value: {{ include "bkflow-common.fullname" . }}-redis-master
- name: REDIS_PORT
  value: "6379"
{{- if .Values.redis.auth.enabled }}
- name: REDIS_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow-common.fullname" . }}-redis
      key: redis-password
{{- end }}
{{- end }}
- name: APP_INTERNAL_TOKEN
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow-common.fullname" . }}-secret
      key: app-internal-token
- name: DEFAULT_ENGINE_APP_INTERNAL_TOKEN
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow-common.fullname" . }}-secret
      key: default-engine-internal-token
- name: BKFLOW_DEFAULT_CALLBACK_KEY
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow-common.fullname" . }}-secret
      key: default-callback-key
{{- end }}

{{/*
Interface Module Deployment
*/}}
{{- define "bkflow-common.interface.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-interface
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: interface
spec:
  replicas: {{ .Values.modules.interface.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: interface
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: interface
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: interface
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["./container_start.sh"]
          ports:
            - name: http
              containerPort: {{ .Values.modules.interface.web.port }}
              protocol: TCP
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.interface.moduleType | quote }}
            - name: PORT
              value: {{ .Values.modules.interface.web.port | quote }}
            - name: GUNICORN_WORKER_NUM
              value: {{ .Values.modules.interface.web.gunicorn.workerNum | quote }}
            - name: GUNICORN_THREAD_NUM
              value: {{ .Values.modules.interface.web.gunicorn.threadNum | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe.httpGet | nindent 12 }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe.httpGet | nindent 12 }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          resources:
            {{- toYaml .Values.modules.interface.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{/*
Interface Celery Beat Deployment
*/}}
{{- define "bkflow-common.interface.celerybeat.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-interface-celerybeat
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: interface-celerybeat
spec:
  replicas: {{ .Values.modules.interface.processes.celerybeat.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: interface-celerybeat
  template:
    metadata:
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: interface-celerybeat
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: celerybeat
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - {{ .Values.modules.interface.processes.celerybeat.command | quote }}
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.interface.moduleType | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          resources:
            {{- toYaml .Values.modules.interface.processes.celerybeat.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{/*
Interface Celery Worker Deployment
*/}}
{{- define "bkflow-common.interface.celeryworker.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bkflow-common.fullname" . }}-interface-celeryworker
  labels:
    {{- include "bkflow-common.labels" . | nindent 4 }}
    app.kubernetes.io/component: interface-celeryworker
spec:
  replicas: {{ .Values.modules.interface.processes.celeryworker.replicas }}
  selector:
    matchLabels:
      {{- include "bkflow-common.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: interface-celeryworker
  template:
    metadata:
      labels:
        {{- include "bkflow-common.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: interface-celeryworker
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bkflow-common.fullname" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: celeryworker
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "bkflow-common.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - {{ .Values.modules.interface.processes.celeryworker.command | quote }}
          env:
            - name: BKFLOW_MODULE_TYPE
              value: {{ .Values.modules.interface.moduleType | quote }}
            {{- include "bkflow-common.commonEnv" . | nindent 12 }}
          resources:
            {{- toYaml .Values.modules.interface.processes.celeryworker.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
