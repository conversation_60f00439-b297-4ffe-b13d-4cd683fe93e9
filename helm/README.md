# BKFlow - 蓝鲸流程引擎服务

## 简介

BKFlow 是一款基于 Python 实现的面向平台、高效灵活的流程引擎平台，旨在助力接入系统快速获取流程执行能力。

BKFlow 提供三大核心功能服务：
- 流程编排和画布嵌入：直观地创建、编辑和管理流程，支持自定义权限控制。
- 流程任务执行能力：通过 API 实现流程任务的创建、执行和控制。
- 决策引擎能力：可在流程中进行规则管理和决策。

BKFlow Helm Chart 部署以下组件：

### Interface 模块

1. **bkflow-interface**
   - 提供 API 接口服务，实现流程编排、任务管理等功能
   - 处理用户请求，提供 Web UI 服务

2. **bkflow-interface-beat**
   - 负责 Celery 周期任务的调度工作
   - 分发任务至 Celery worker

3. **bkflow-interface-worker**
   - 负责执行 Interface 模块的 Celery 异步任务

### Engine 模块

1. **bkflow-engine**
   - 提供流程引擎的 API 接口服务
   - 处理流程执行相关的请求

2. **bkflow-engine-beat**
   - 负责 Engine 模块的 Celery 周期任务调度

3. **bkflow-engine-er-execute**
   - 负责执行流程引擎的执行队列任务

4. **bkflow-engine-er-schedule**
   - 负责执行流程引擎的调度队列任务

5. **bkflow-engine-common-worker**
   - 负责执行流程引擎的通用任务

6. **bkflow-engine-timeout-worker**
   - 负责处理节点超时相关的任务

7. **bkflow-engine-clean-worker**
   - 负责执行清理任务

8. **bkflow-init-job**
   - 部署后执行的初始化作业，包括数据库迁移、创建缓存表等

## 组件依赖
- MySQL (5.7+)
- Redis (4.0+)
- RabbitMQ (3.8+)
- 蓝鲸 PaaS 平台
- 蓝鲸 API 网关
- 蓝鲸统一登录

## 准备工作
1. 确保 Kubernetes 集群已经安装
2. 确保 Helm 已经安装
3. 准备 MySQL、Redis 和 RabbitMQ 服务

## 安装 Chart
使用以下命令安装 release `bkflow`
```shell
$ helm repo add blueking <HELM_REPO_URL>
$ helm install bkflow blueking/bkflow -f values.yaml --namespace bkflow
```

## 卸载 Chart
使用以下命令卸载 release `bkflow`
```shell
helm uninstall bkflow --namespace bkflow
```

## 配置参数
<!-- parameters -->
### 通用配置

|配置项|说明|示例|
|--|--|--|
|`image.registry`|镜像服务地址|`hub.bktencent.com`|
|`image.repository`|仓库路径|`blueking/bkbase-datamanageapi`|
|`image.tag`|标签|`1.1.4`|
|`image.pullPolicy`|镜像拉取策略|`IfNotPresent`|
### 资源配置

|配置项|说明|示例|
|--|--|--|
|`resources.limits.cpu`|cpu limit|`1`|
|`resources.limits.memory`|memory limit|`1Gi`|
|`resources.requests.cpu`|cpu limit|`1`|
|`resources.requests.memory`|memory limit|`1Gi`|
### API配置

|配置项|说明|示例|
|--|--|--|
|`pizza.env.AUTH_API_HOST`|authapi地址|`"bkbase-authapi.example.com"`|
|`pizza.env.AUTH_API_PORT`|authapi端口|`80`|
|`pizza.env.META_API_HOST`|metaapi地址|`"bkbase-metaapi.example.com"`|
|`pizza.env.META_API_PORT`|metaapi端口|`80`|
|`pizza.env.SMCS_API_URL`|paas 发送消息地址|`"smcs.example.com"`|
|`pizza.env.PAAS_HOST`|蓝鲸paas 地址|`"paas.example.com"`|
|`pizza.env.PAAS_HTTP_PORT`|蓝鲸paas 端口|`80`|
|`pizza.env.APP_ID`|bkbase app id|`"bk_bkdata"`|
|`pizza.env.APP_TOKEN`|bkbase app token|`"xxxxx_token"`|
|`pizza.env.CRYPT_ROOT_KEY`|内部加解密key|`"xxxxx_key"`|
|`pizza.env.CRYPT_ROOT_IV`|内部加解密iv|`"xxxxx_iv"`|
|`pizza.env.CRYPT_INSTANCE_KEY`|内部加解密instance key|`"xxxxx_ie"`|
|`pizza.env.CONFIG_DB_HOST`|datahub数据库地址|`"127.0.0.1"`|
|`pizza.env.CONFIG_DB_PORT`|datahub数据库端口|`3306`|
|`pizza.env.CONFIG_DB_USER`|datahub数据库用户|`"root"`|
|`pizza.env.CONFIG_DB_PASSWORD`|datahub数据库密码|`"example"`|
|`api.env.ALERT_TITLE`|告警邮件中文标题|`"《运维基础计算平台告警》"`|
|`api.env.ALERT_TITLE_EN`|告警邮件英文标题|`"《Base Calculate Platform Alert》"`|
|`api.env.BKDATA_BKSQL_EXTEND_HOST`|bksql extend地址|`""`|
|`api.env.BKDATA_BKSQL_EXTEND_PORT`|bksql extend端口|`8000`|
|`api.env.BKDATA_BKSQL_FLINKSQL_HOST`|bksql flinksql地址|`""`|
|`api.env.BKDATA_BKSQL_FLINKSQL_PORT`|bksql flinksql端口|`8000`|
|`api.env.BKDATA_BKSQL_HOST`|bksql 地址|`""`|
|`api.env.BKDATA_BKSQL_PORT`|bksql 端口|`8000`|
|`api.env.BKDATA_BKSQL_SPARKSQL_HOST`|bksql sparksql地址|`""`|
|`api.env.BKDATA_BKSQL_SPARKSQL_PORT`|bksql sparksql端口|`8000`|
|`api.env.BKDATA_DATAFLOWAPI_HOST`|dataflowapi地址|`""`|
|`api.env.BKDATA_DATAFLOWAPI_PORT`|dataflowapi端口|`8000`|
|`api.env.BKDATA_DATAHUBAPI_HOST`|datahubapi地址|`""`|
|`api.env.BKDATA_DATAHUBAPI_PORT`|datahubapi端口|`8000`|
|`api.env.BKDATA_DATAMANAGEAPI_HOST`|datamanageapi地址|`""`|
|`api.env.BKDATA_DATAMANAGEAPI_PORT`|datamanageapi端口|`8000`|
|`api.env.BKDATA_INFLUXDB_PROXY_HOST`|influxdb地址|`""`|
|`api.env.BKDATA_INFLUXDB_PROXY_PORT`|influxdb端口|`8000`|
|`api.env.BKDATA_QUERYENGINEAPI_HOST`|queryengineapi地址|`""`|
|`api.env.BKDATA_QUERYENGINEAPI_PORT`|queryengineapi端口|`8000`|
|`api.env.BKDATA_RABBITMQ_HOST`|rabbitmq地址|`"bkbase-rabbitmq"`|
|`api.env.BKDATA_RABBITMQ_PASS`|rabbitmq密码|`"guest"`|
|`api.env.BKDATA_RABBITMQ_PORT`|rabbitmq端口|`5672`|
|`api.env.BKDATA_RABBITMQ_USER`|rabbitmq用户|`"guest"`|
|`api.env.BKDATA_RABBITMQ_VHOST`|rabbitmq vhost 配置|`""`|
|`api.env.BKDATA_TRACE_ENABLED`|是否启动数据足迹|`"false"`|
|`api.env.BKDATA_TRACE_ES_API_HOST`|数据足迹依赖 es 存储地址|`""`|
|`api.env.BKDATA_TRACE_ES_API_PORT`|数据足迹依赖 es 存储端口|`80`|
|`api.env.BKDATA_TRACE_ES_PASS_TOKEN`|数据足迹依赖 es 密钥|`""`|
|`api.env.BKDATA_TRACE_ES_USER`|数据足迹依赖 es 账户|`""`|
|`api.env.ES_USER`|数据足迹依赖 es 账户|`""`|
|`api.env.INFLUXDB_BKDATA_PASS`|influxdb密码|`""`|
|`api.env.INFLUXDB_BKDATA_USER`|influxdb用户|`""`|
|`api.env.MYSQL_DATAFLOW_IP0`|dataflow mysql地址|`"bkbase-mysql"`|
|`api.env.MYSQL_DATAFLOW_PASS`|dataflow mysql密码|`"example"`|
|`api.env.MYSQL_DATAFLOW_PORT`|dataflow mysql端口|`3306`|
|`api.env.MYSQL_DATAFLOW_USER`|dataflow mysql用户|`"root"`|
|`api.env.MYSQL_DATAHUB_IP0`|datahub mysql地址|`"bkbase-mysql"`|
|`api.env.MYSQL_DATAHUB_PASS`|datahub mysql密码|`"example"`|
|`api.env.MYSQL_DATAHUB_PORT`|datahub mysql端口|`3306`|
|`api.env.MYSQL_DATAHUB_USER`|datahub mysql用户|`"root"`|
|`api.env.MYSQL_JOBNAVI_IP0`|jobnavi mysql地址|`"bkbase-mysql"`|
|`api.env.MYSQL_JOBNAVI_PASS`|jobnavi mysql密码|`"example"`|
|`api.env.MYSQL_JOBNAVI_PORT`|jobnavi mysql端口|`3306`|
|`api.env.MYSQL_JOBNAVI_USER`|jobnavi mysql用户|`"root"`|
|`api.env.MYSQL_JOBNAVI_DB`|jobnavi mysql数据库名|`""`|
|`api.env.MYSQL_DATALOG_IP0`|datalog mysql地址|`"bkbase-mysql"`|
|`api.env.MYSQL_DATALOG_PASS`|datalog mysql密码|`"example"`|
|`api.env.MYSQL_DATALOG_PORT`|datalog mysql端口|`3306`|
|`api.env.MYSQL_DATALOG_USER`|datalog mysql用户|`"root"`|
|`api.env.REDIS_BKDATA_HOST`|redis地址|`"bkbase-redis"`|
|`api.env.REDIS_BKDATA_PASS`|redis密码|`"example"`|
|`api.env.REDIS_BKDATA_PORT`|redis端口|`6379`|
|`api.env.REDIS_MASTER_NAME`|redis master名称|`""`|
|`api.env.ZK_CONFIG_HOST`|zk地址|`""`|
|`api.env.ZK_PORT`|zk端口|`2181`|
### postinstall配置

|配置项|说明|示例|
|--|--|--|
|`postinstall.enabled`|是否启用importms|`true`|
|`postinstall.resources.limits.cpu`|cpu limit|`1`|
|`postinstall.resources.limits.memory`|memory limit|`1Gi`|
|`postinstall.resources.requests.cpu`|cpu limit|`1`|
|`postinstall.resources.requests.memory`|memory limit|`1Gi`|
### importms配置

|配置项|说明|示例|
|--|--|--|
|`importms.enabled`|是否启用importms|`true`|
|`importms.resources.limits.cpu`|cpu limit|`1`|
|`importms.resources.limits.memory`|memory limit|`1Gi`|
|`importms.resources.requests.cpu`|cpu limit|`1`|
|`importms.resources.requests.memory`|memory limit|`1Gi`|
### celeryworker配置

|配置项|说明|示例|
|--|--|--|
|`celeryworker.enabled`|是否启用celeryworker|`true`|
|`celeryworker.replicas`|celeryworker实例数|`1`|
|`celeryworker.resources.limits.cpu`|cpu limit|`1`|
|`celeryworker.resources.limits.memory`|memory limit|`1Gi`|
|`celeryworker.resources.requests.cpu`|cpu limit|`1`|
|`celeryworker.resources.requests.memory`|memory limit|`1Gi`|
### celerybeat配置

|配置项|说明|示例|
|--|--|--|
|`celerybeat.enabled`|是否启用celerybeat|`true`|
|`celerybeat.replicas`|celerybeat实例数|`1`|
|`celerybeat.resources.limits.cpu`|cpu limit|`1`|
|`celerybeat.resources.limits.memory`|memory limit|`1Gi`|
|`celerybeat.resources.requests.cpu`|cpu limit|`1`|
|`celerybeat.resources.requests.memory`|memory limit|`1Gi`|
### 日志采集配置

|配置项|说明|示例|
|--|--|--|
### 活跃探测器配置

|配置项|说明|示例|
|--|--|--|
|`livenessProbe.enabled`|是否启用活跃探测器|`true`|
|`livenessProbe.initialDelaySeconds`|初次探测前等待时长|`10`|
|`livenessProbe.periodSeconds`|探测间隔|`60`|
|`livenessProbe.timeoutSeconds`|超时等待时长|`10`|
|`livenessProbe.successThreshold`|最小连续成功数|`1`|
|`livenessProbe.failureThreshold`|探测失败重试次数|`3`|
|`livenessProbe.urlpath`|url路径|`/v3/datamanage/heartbeat/`|
|`livenessProbe.port`|端口|`8000`|
### 就绪探测器配置

|配置项|说明|示例|
|--|--|--|
|`readinessProbe.enabled`|是否启用就绪探测器|`true`|
|`readinessProbe.initialDelaySeconds`|初次探测前等待时长|`30`|
|`readinessProbe.periodSeconds`|探测间隔|`60`|
|`readinessProbe.timeoutSeconds`|超时等待时长|`10`|
|`readinessProbe.successThreshold`|最小连续成功数|`1`|
|`readinessProbe.failureThreshold`|探测失败重试次数|`3`|
|`readinessProbe.urlpath`|url路径|`/v3/datamanage/heartbeat/`|
|`readinessProbe.port`|端口|`8000`|
<!-- end of parameters -->

## 安装和配置细节
None

## 问题排查
当执行出错时，可通过以下命令查看日志
``` shell
kubectl -n bkbase logs -f <BKBASE_DATAMANAGEAPI_PODID>
```

## 升级
None
