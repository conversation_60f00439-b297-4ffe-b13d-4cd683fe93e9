# BKFlow Helm Chart

![Version: 1.11.1](https://img.shields.io/badge/Version-1.11.1-informational?style=flat-square) ![Type: application](https://img.shields.io/badge/Type-application-informational?style=flat-square) ![AppVersion: 1.11.1](https://img.shields.io/badge/AppVersion-1.11.1-informational?style=flat-square)

蓝鲸流程引擎服务 (BKFlow) Helm Chart

## 概述

BKFlow 是蓝鲸流程引擎服务，提供强大的工作流编排和执行能力。本 Helm Chart 支持模块化部署，包括：

- **Interface 模块**：作为 BKFlow 的入口模块，负责空间、流程、凭证等上层资源的管理
- **Engine 模块**：作为 BKFlow 任务的执行模块，与空间强绑定，基于空间 ID 进行路由，负责任务执行

## 架构特点

### 模块化设计
- **Interface 模块**：处理 API 请求，管理业务逻辑
- **Engine 模块**：执行具体的工作流任务，支持多实例部署

### 进程分离
- **Web 进程**：处理 HTTP 请求
- **Celery Beat**：定时任务调度
- **Celery Worker**：异步任务处理
- **ER Execute/Schedule**：引擎执行和调度进程
- **Timeout Worker**：超时处理
- **Clean Worker**：清理任务

## 快速开始

### 安装

```bash
# 添加依赖
helm dependency update

# 安装 BKFlow
helm install bkflow . -n bkflow --create-namespace

# 使用自定义配置安装
helm install bkflow . -n bkflow --create-namespace -f custom-values.yaml
```

### 升级

```bash
helm upgrade bkflow . -n bkflow
```

### 卸载

```bash
helm uninstall bkflow -n bkflow
```

## 配置说明

### 模块配置

#### Interface 模块
```yaml
modules:
  interface:
    enabled: true
    replicas: 2
    web:
      port: 5000
      gunicorn:
        workerNum: 4
        threadNum: 10
    processes:
      celerybeat:
        enabled: true
        replicas: 1
      celeryworker:
        enabled: true
        replicas: 2
```

#### Engine 模块
```yaml
modules:
  engine:
    enabled: true
    moduleCode: default  # 空间路由标识
    replicas: 2
    processes:
      er_e:
        enabled: true
        replicas: 5
      er_s:
        enabled: true
        replicas: 5
      cworker:
        enabled: true
        replicas: 5
```

### 依赖服务

```yaml
# MySQL 数据库
mysql:
  enabled: true
  auth:
    database: "bkflow"
    username: "bkflow"
    password: "bkflow123"

# RabbitMQ 消息队列
rabbitmq:
  enabled: true
  auth:
    username: "bkflow"
    password: "bkflow123"

# Redis 缓存
redis:
  enabled: true
  auth:
    enabled: true
    password: "bkflow123"
```

## Requirements

| Repository | Name | Version |
|------------|------|---------|
| file://./charts/bkflow-common | bkflow-common | ~0.1.2 |
| https://charts.bitnami.com/bitnami | common | ~1.10.0 |
| https://charts.bitnami.com/bitnami | mysql | ~9.x.x |
| https://charts.bitnami.com/bitnami | rabbitmq | ~11.x.x |
| https://charts.bitnami.com/bitnami | redis | ~17.x.x |

## 主要配置参数

### 全局配置

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `global.imageRegistry` | 全局镜像仓库地址 | `""` |
| `global.imagePullSecrets` | 镜像拉取密钥 | `[]` |

### 镜像配置

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `image.registry` | 镜像仓库地址 | `hub.bktencent.com` |
| `image.repository` | 镜像仓库路径 | `blueking/bkflow` |
| `image.tag` | 镜像标签 | `1.11.1` |
| `image.pullPolicy` | 镜像拉取策略 | `IfNotPresent` |

### 应用配置

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `app.bkAppCode` | 蓝鲸应用编码 | `bk_flow_engine` |
| `app.bkAppName` | 蓝鲸应用名称 | `蓝鲸流程引擎服务` |
| `app.usePluginService` | 是否使用插件服务 | `true` |

### 服务配置

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `service.type` | 服务类型 | `ClusterIP` |
| `service.port` | 服务端口 | `80` |
| `service.targetPort` | 目标端口 | `5000` |

### Ingress 配置

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `ingress.enabled` | 是否启用 Ingress | `false` |
| `ingress.className` | Ingress 类名 | `""` |
| `ingress.hosts` | 主机配置 | `[{host: "bkflow.local", paths: [{path: "/", pathType: "Prefix"}]}]` |

## 监控和日志

### 健康检查

```yaml
livenessProbe:
  enabled: true
  httpGet:
    path: /healthz
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  enabled: true
  httpGet:
    path: /healthz
    port: http
  initialDelaySeconds: 5
  periodSeconds: 5
```

### 日志收集

```yaml
bkLogConfig:
  enabled: false
  dataId: 1
```

## 故障排除

### 常见问题

1. **Pod 启动失败**
   ```bash
   kubectl describe pod -n bkflow -l app.kubernetes.io/name=bkflow
   kubectl logs -n bkflow -l app.kubernetes.io/name=bkflow
   ```

2. **数据库连接问题**
   ```bash
   kubectl exec -it -n bkflow deployment/bkflow-interface -- python manage.py dbshell
   ```

3. **消息队列连接问题**
   ```bash
   kubectl port-forward -n bkflow svc/bkflow-rabbitmq 15672:15672
   # 访问 http://localhost:15672
   ```

### 调试命令

```bash
# 查看所有资源
kubectl get all -n bkflow

# 查看配置
kubectl get configmap -n bkflow bkflow-config -o yaml

# 查看密钥
kubectl get secret -n bkflow bkflow-secret -o yaml

# 扩容 Interface 模块
kubectl scale deployment bkflow-interface -n bkflow --replicas=3
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个 Helm Chart。

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](../LICENSE) 文件。
