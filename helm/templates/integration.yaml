{{- if .Values.modules.interface.enabled }}
# Interface模块部署
{{ include "bkflow-common.interface.deployment" . }}
---
{{ include "bkflow-common.interface.service" . }}
---
{{ include "bkflow-common.interface.ingress" . }}

{{- if .Values.modules.interface.processes.celerybeat.enabled }}
---
{{ include "bkflow-common.interface.celerybeat.deployment" . }}
{{- end }}

{{- if .Values.modules.interface.processes.celeryworker.enabled }}
---
{{ include "bkflow-common.interface.celeryworker.deployment" . }}
{{- end }}
{{- end }}

{{- if .Values.modules.engine.enabled }}
# Engine模块部署
{{ include "bkflow-common.engine.deployment" . }}
---
{{ include "bkflow-common.engine.service" . }}

{{- if .Values.modules.engine.processes.er_e.enabled }}
---
{{ include "bkflow-common.engine.er_e.deployment" . }}
{{- end }}

{{- if .Values.modules.engine.processes.er_s.enabled }}
---
{{ include "bkflow-common.engine.er_s.deployment" . }}
{{- end }}

{{- if .Values.modules.engine.processes.cworker.enabled }}
---
{{ include "bkflow-common.engine.cworker.deployment" . }}
{{- end }}
{{- end }}

{{- if .Values.bkLogConfig.enabled }}
---
{{ include "bkflow-common.bklogconfig.stdout" . }}
---
{{ include "bkflow-common.bklogconfig.apilog" . }}
---
{{ include "bkflow-common.bklogconfig.syslog" . }}
{{- end }}