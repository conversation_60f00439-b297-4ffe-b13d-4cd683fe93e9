{{- if .Values.modules.interface.enabled }}
# Interface 模块部署 - 入口模块，管理空间、流程、凭证等上层资源
{{ include "bkflow-common.interface.deployment" . }}
---
{{ include "bkflow-common.interface.service" . }}
---
{{ include "bkflow-common.interface.ingress" . }}

{{- if .Values.modules.interface.processes.celerybeat.enabled }}
---
{{ include "bkflow-common.interface.celerybeat.deployment" . }}
{{- end }}

{{- if .Values.modules.interface.processes.celeryworker.enabled }}
---
{{ include "bkflow-common.interface.celeryworker.deployment" . }}
{{- end }}
{{- end }}

{{- if .Values.modules.engine.enabled }}
# Engine 模块部署 - 任务执行模块，与空间强绑定，基于空间 ID 进行路由
{{ include "bkflow-common.engine.deployment" . }}
---
{{ include "bkflow-common.engine.service" . }}

{{- if .Values.modules.engine.processes.er_e.enabled }}
---
{{ include "bkflow-common.engine.er_e.deployment" . }}
{{- end }}

{{- if .Values.modules.engine.processes.er_s.enabled }}
---
{{ include "bkflow-common.engine.er_s.deployment" . }}
{{- end }}

{{- if .Values.modules.engine.processes.cworker.enabled }}
---
{{ include "bkflow-common.engine.cworker.deployment" . }}
{{- end }}

{{- if .Values.modules.engine.processes.timeout.enabled }}
---
{{ include "bkflow-common.engine.timeout.deployment" . }}
{{- end }}

{{- if .Values.modules.engine.processes.beat.enabled }}
---
{{ include "bkflow-common.engine.beat.deployment" . }}
{{- end }}

{{- if .Values.modules.engine.processes.clean_worker.enabled }}
---
{{ include "bkflow-common.engine.clean_worker.deployment" . }}
{{- end }}
{{- end }}

# 通用资源
---
{{ include "bkflow-common.secret" . }}
---
{{ include "bkflow-common.configmap" . }}
---
{{ include "bkflow-common.serviceaccount" . }}

{{- if .Values.bkLogConfig.enabled }}
---
{{ include "bkflow-common.bklogconfig.stdout" . }}
---
{{ include "bkflow-common.bklogconfig.apilog" . }}
---
{{ include "bkflow-common.bklogconfig.syslog" . }}
{{- end }}