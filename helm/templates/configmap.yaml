apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "bkflow.fullname" . }}-config
  labels:
    {{- include "bkflow.labels" . | nindent 4 }}
data:
  # BKFlow 应用配置
  BK_APP_CODE: {{ .Values.app.bkAppCode | quote }}
  BK_APP_NAME: {{ .Values.app.bkAppName | quote }}
  BK_APIGW_NAME: {{ .Values.app.apigwName | quote }}
  BK_APIGW_NETLOC_PATTERN: {{ .Values.app.apigwNetlocPattern | quote }}
  BKAPP_USE_PLUGIN_SERVICE: {{ .Values.app.usePluginService | quote }}
  
  # 通用环境变量
  {{- range $key, $value := .Values.env }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
