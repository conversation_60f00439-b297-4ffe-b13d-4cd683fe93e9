CHART NAME: {{ .Chart.Name }}
CHART VERSION: {{ .Chart.Version }}
APP VERSION: {{ .Chart.AppVersion }}

集群内服务地址：

    echo "URL : http://{{ include "common.names.fullname" . }}:{{ .Values.api.http.port }}/v3/{{ .Values.api.env.APP_NAME }}/"

功能测试（访问健康检测接口）：

    export POD_NAME=$(kubectl get pods -n {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "common.names.fullname" . }}" -o jsonpath="{.items[0].metadata.name}")
    kubectl exec -it $POD_NAME -- curl http://{{ include "common.names.fullname" . }}:{{ .Values.api.http.port }}/v3/{{ .Values.api.env.APP_NAME }}/healthz/
